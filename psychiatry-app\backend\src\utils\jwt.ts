import jwt, { SignOptions } from 'jsonwebtoken';
import { TokenPayload, RefreshTokenPayload } from '@/types';

/**
 * JWT utility functions for token generation and verification
 * Implements secure token management with access and refresh tokens
 */

const JWT_SECRET = process.env.JWT_SECRET;
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

if (!JWT_SECRET || !JWT_REFRESH_SECRET) {
  throw new Error('JWT secrets must be defined in environment variables');
}

/**
 * Generate access token for authenticated user
 */
export const generateAccessToken = (payload: {
  userId: string;
  username: string;
  role: string;
}): string => {
  const options: SignOptions = {
    expiresIn: JWT_EXPIRES_IN as any,
    issuer: 'psychiatry-app',
    audience: 'psychiatry-app-users',
  };
  return jwt.sign(payload, JWT_SECRET!, options);
};

/**
 * Generate refresh token for token renewal
 */
export const generateRefreshToken = (payload: {
  userId: string;
  tokenId: string;
}): string => {
  const options: SignOptions = {
    expiresIn: JWT_REFRESH_EXPIRES_IN as any,
    issuer: 'psychiatry-app',
    audience: 'psychiatry-app-users',
  };
  return jwt.sign(payload, JWT_REFRESH_SECRET!, options);
};

/**
 * Verify access token and return payload
 */
export const verifyAccessToken = (token: string): TokenPayload => {
  try {
    const payload = jwt.verify(token, JWT_SECRET, {
      issuer: 'psychiatry-app',
      audience: 'psychiatry-app-users',
    }) as TokenPayload;
    
    return payload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Access token expired');
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid access token');
    }
    throw new Error('Token verification failed');
  }
};

/**
 * Verify refresh token and return payload
 */
export const verifyRefreshToken = (token: string): RefreshTokenPayload => {
  try {
    const payload = jwt.verify(token, JWT_REFRESH_SECRET, {
      issuer: 'psychiatry-app',
      audience: 'psychiatry-app-users',
    }) as RefreshTokenPayload;
    
    return payload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Refresh token expired');
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid refresh token');
    }
    throw new Error('Refresh token verification failed');
  }
};

/**
 * Extract token from Authorization header
 */
export const extractTokenFromHeader = (authHeader: string | undefined): string | null => {
  if (!authHeader) {
    return null;
  }
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
};

/**
 * Get token expiration date
 */
export const getTokenExpiration = (token: string): Date | null => {
  try {
    const decoded = jwt.decode(token) as { exp?: number };
    if (decoded?.exp) {
      return new Date(decoded.exp * 1000);
    }
    return null;
  } catch {
    return null;
  }
};

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  const expiration = getTokenExpiration(token);
  if (!expiration) {
    return true;
  }
  return expiration.getTime() < Date.now();
};
