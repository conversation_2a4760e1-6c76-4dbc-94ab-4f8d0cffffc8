import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { AuthService } from '@/services/authService';
import { formatErrorResponse } from '@/utils/errors';
import { AuthRequest, RegisterData, LoginCredentials } from '@/types';
import { registerSchema, loginSchema } from '../utils/validation';

/**
 * Authentication controller handling HTTP requests for auth operations
 */

const refreshTokenSchema = z.object({
  refreshToken: z.string()
    .min(1, 'Refresh token is required'),
});

const changePasswordSchema = z.object({
  currentPassword: z.string()
    .min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'New password must be at least 8 characters'),
});

export class AuthController {
  /**
   * Register a new user
   * POST /api/auth/register
   */
  static async register(req: Request, res: Response, next: NextFunction) {
    try {
      const validatedData = registerSchema.parse(req.body) as RegisterData;
      const result = await AuthService.register(validatedData);
      
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Login user
   * POST /api/auth/login
   */
  static async login(req: Request, res: Response, next: NextFunction) {
    try {
      const validatedData = loginSchema.parse(req.body) as LoginCredentials;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent');

      const result = await AuthService.login(validatedData, ipAddress, userAgent);
      
      // Set refresh token as httpOnly cookie
      res.cookie('refreshToken', result.data?.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });
      
      res.status(200).json({
        success: result.success,
        data: {
          user: result.data?.user,
          accessToken: result.data?.accessToken,
        },
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Refresh access token
   * POST /api/auth/refresh
   */
  static async refreshToken(req: Request, res: Response, next: NextFunction) {
    try {
      // Get refresh token from cookie or body
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
      
      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          error: 'Refresh token is required',
        });
      }
      
      const result = await AuthService.refreshToken(refreshToken);
      
      // Set new refresh token as httpOnly cookie
      res.cookie('refreshToken', result.data?.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });
      
      res.status(200).json({
        success: result.success,
        data: {
          accessToken: result.data?.accessToken,
        },
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Logout user
   * POST /api/auth/logout
   */
  static async logout(req: Request, res: Response, next: NextFunction) {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
      
      if (refreshToken) {
        await AuthService.logout(refreshToken);
      }
      
      // Clear refresh token cookie
      res.clearCookie('refreshToken');
      
      res.status(200).json({
        success: true,
        data: null,
        message: 'Logout successful',
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get current user information
   * GET /api/auth/me
   */
  static async getCurrentUser(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }
      
      const result = await AuthService.getCurrentUser(req.user.id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Change user password
   * PUT /api/auth/change-password
   */
  static async changePassword(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }
      
      const validatedData = changePasswordSchema.parse(req.body);
      const result = await AuthService.changePassword(
        req.user.id,
        validatedData.currentPassword,
        validatedData.newPassword
      );
      
      // Clear refresh token cookie to force re-login
      res.clearCookie('refreshToken');
      
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }
}
