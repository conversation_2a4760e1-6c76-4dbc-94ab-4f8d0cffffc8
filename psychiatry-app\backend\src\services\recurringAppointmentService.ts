import { PrismaClient } from '@prisma/client';
import { addDays, addWeeks, addMonths, addYears, isBefore, isAfter, format } from 'date-fns';
import { 
  CreateRecurringAppointmentData, 
  ApiResponse, 
  AuditLogData 
} from '@/types';
import { NotFoundError, ValidationError, ConflictError } from '@/utils/errors';
import { AppointmentService } from './appointmentService';

const prisma = new PrismaClient();

/**
 * Recurring appointment service for managing appointment series
 * Handles creation, modification, and cancellation of recurring appointments
 */
export class RecurringAppointmentService {
  /**
   * Create a recurring appointment series
   */
  static async createRecurringAppointment(
    data: CreateRecurringAppointmentData,
    createdBy: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ recurringAppointment: any; appointments: any[] }>> {
    // Validate required fields
    if (!data.patientId || !data.providerId || !data.startDate || !data.frequency) {
      throw new ValidationError('Patient ID, provider ID, start date, and frequency are required');
    }

    // Validate recurrence pattern
    if (!['DAILY', 'WEEKLY', 'BIWEEKLY', 'MONTHLY', 'YEARLY'].includes(data.frequency)) {
      throw new ValidationError('Invalid recurrence frequency');
    }

    // Validate dates
    const startDate = new Date(data.startDate);
    const endDate = data.endDate ? new Date(data.endDate) : null;

    if (isNaN(startDate.getTime()) || isBefore(startDate, new Date())) {
      throw new ValidationError('Invalid start date or date is in the past');
    }

    if (endDate && (isNaN(endDate.getTime()) || isBefore(endDate, startDate))) {
      throw new ValidationError('End date must be after start date');
    }

    // Validate occurrence count vs end date
    if (!endDate && !data.maxOccurrences) {
      throw new ValidationError('Either end date or max occurrences must be provided');
    }

    // Check if patient and provider exist
    const [patient, provider] = await Promise.all([
      prisma.patient.findFirst({
        where: { id: data.patientId, isDeleted: false },
      }),
      prisma.user.findFirst({
        where: { 
          id: data.providerId, 
          isActive: true,
          role: { in: ['ADMIN', 'CLINICIAN'] },
        },
      }),
    ]);

    if (!patient) {
      throw new NotFoundError('Patient not found');
    }

    if (!provider) {
      throw new NotFoundError('Provider not found or not authorized');
    }

    // Create recurring appointment record
    const recurringAppointment = await prisma.recurringAppointment.create({
      data: {
        patientId: data.patientId,
        providerId: data.providerId,
        startDate: startDate,
        endDate: endDate,
        duration: data.duration,
        type: data.type,
        frequency: data.frequency,
        interval: data.interval || 1,
        dayOfWeek: data.dayOfWeek,
        dayOfMonth: data.dayOfMonth,
        timeSlot: data.timeSlot,
        // title: data.title?.trim() || null, // Removed - field doesn't exist in schema
        // description: data.description?.trim() || null, // Removed - field doesn't exist in schema
        // location: data.location?.trim() || null, // Removed - field doesn't exist in schema
        // isVirtual: data.isVirtual || false, // Removed - field doesn't exist in schema
        // virtualMeetingUrl: data.virtualMeetingUrl?.trim() || null, // Removed - field doesn't exist in schema
        notes: data.notes?.trim() || null,
        maxOccurrences: data.maxOccurrences || null,
        isActive: true,
      },
      include: {
        patient: true,
        provider: true,
      },
    });

    // Schedule initial appointments
    await this.scheduleAppointmentsForRecurring(recurringAppointment.id);

    // Create audit log
    await this.createAuditLog({
      userId: createdBy,
      action: 'CREATE',
      entityType: 'RECURRING_APPOINTMENT',
      entityId: recurringAppointment.id,
      patientId: data.patientId,
      newValues: {
        frequency: data.frequency,
        startDate: startDate,
        endDate: endDate,
        maxOccurrences: data.maxOccurrences,
      },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: {
        recurringAppointment: {
          ...recurringAppointment,
          patient,
          provider,
        },
        appointments: [], // Add empty appointments array for now
      },
      message: `Created recurring appointment series`,
    };
  }

  /**
   * Schedule initial appointments for a recurring appointment
   */
  private static async scheduleAppointmentsForRecurring(id: string): Promise<void> {
    const recurringAppointment = await prisma.recurringAppointment.findUnique({
      where: { id },
    });

    if (!recurringAppointment) return;

    const maxOccurrences = recurringAppointment.maxOccurrences || 52; // Default max 1 year
    let currentDate = new Date(recurringAppointment.startDate);
    const endDate = recurringAppointment.endDate ? new Date(recurringAppointment.endDate) : null;
    let occurrences = 0;

    while (occurrences < maxOccurrences && (!endDate || currentDate <= endDate)) {
      // Check if an appointment already exists for this slot
      const existing = await prisma.appointment.findFirst({
        where: {
          recurringAppointmentId: recurringAppointment.id,
          date: currentDate,
        },
      });

      if (!existing) {
        await AppointmentService.createAppointment({
          patientId: recurringAppointment.patientId,
          providerId: recurringAppointment.providerId!,
          date: currentDate.toISOString(),
          duration: recurringAppointment.duration,
          type: recurringAppointment.type as 'INITIAL_CONSULTATION' | 'FOLLOW_UP' | 'THERAPY_SESSION' | 'MEDICATION_REVIEW' | 'CRISIS_INTERVENTION' | 'GROUP_THERAPY' | 'FAMILY_THERAPY' | 'PSYCHOLOGICAL_TESTING' | 'OTHER',
          // title: recurringAppointment.title || undefined, // Field doesn't exist in schema
          // description: recurringAppointment.description || undefined, // Field doesn't exist in schema
          // location: recurringAppointment.location || undefined, // Field doesn't exist in schema
          // isVirtual: recurringAppointment.isVirtual, // Field doesn't exist in schema
          // virtualMeetingUrl: recurringAppointment.virtualMeetingUrl || undefined, // Field doesn't exist in schema
          notes: `Recurring appointment: ${recurringAppointment.id}`,
          status: 'SCHEDULED',
          recurringAppointmentId: recurringAppointment.id,
        }, recurringAppointment.providerId!);
      }

      // Calculate next date
      // TODO: Implement calculateNextOccurrence method
      // currentDate = this.calculateNextOccurrence(
      //   currentDate,
      //   recurringAppointment.frequency,
      //   recurringAppointment.interval || 1
      // );
      // For now, just add one day to avoid infinite loop
      currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
      occurrences++;
    }
  }

  /**
   * Calculate next occurrence date based on pattern
   */
  private static getNextOccurrence(
    currentDate: Date,
    pattern: string,
    interval: number
  ): Date {
    switch (pattern) {
      case 'DAILY':
        return addDays(currentDate, interval);
      case 'WEEKLY':
        return addWeeks(currentDate, interval);
      case 'BIWEEKLY':
        return addWeeks(currentDate, 2 * interval);
      case 'MONTHLY':
        return addMonths(currentDate, interval);
      case 'YEARLY':
        return addYears(currentDate, interval);
      default:
        throw new Error(`Unsupported recurrence pattern: ${pattern}`);
    }
  }

  /**
   * Update recurring appointment series
   */
  static async updateRecurringAppointment(
    id: string,
    data: Partial<CreateRecurringAppointmentData>,
    updateFutureAppointments: boolean,
    userId: string,
    userRole: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ recurringAppointment: any }>> {
    // Check if recurring appointment exists and user has access
    const existingRecurring = await prisma.recurringAppointment.findFirst({
      where: {
        id,
        ...(userRole === 'CLINICIAN' && { providerId: userId }),
      },
    });

    if (!existingRecurring) {
      throw new NotFoundError('Recurring appointment not found');
    }

    // Build update data object
    const updateData: any = {};
    if (data.startDate) updateData.startDate = new Date(data.startDate);
    if (data.endDate) updateData.endDate = new Date(data.endDate);
    if (data.duration) updateData.duration = data.duration;
    if (data.type) updateData.type = data.type;
    if (data.frequency) updateData.frequency = data.frequency;
    if (data.interval) updateData.interval = data.interval;
    if (data.dayOfWeek) updateData.dayOfWeek = data.dayOfWeek;
    if (data.dayOfMonth) updateData.dayOfMonth = data.dayOfMonth;
    if (data.timeSlot) updateData.timeSlot = data.timeSlot;
    if (data.notes) updateData.notes = data.notes;
    if (data.maxOccurrences) updateData.maxOccurrences = data.maxOccurrences;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;
    if (data.title !== undefined) updateData.title = data.title?.trim() || null;
    if (data.description !== undefined) updateData.description = data.description?.trim() || null;
    if (data.location !== undefined) updateData.location = data.location?.trim() || null;
    if (data.isVirtual !== undefined) updateData.isVirtual = data.isVirtual;
    if (data.virtualMeetingUrl !== undefined) updateData.virtualMeetingUrl = data.virtualMeetingUrl?.trim() || null;

    // Perform update
    const updatedRecurring = await prisma.recurringAppointment.update({
      where: { id },
      data: updateData,
    });

    // Reschedule future appointments
    await prisma.appointment.deleteMany({
      where: {
        recurringAppointmentId: id,
        date: { gte: new Date() },
      },
    });

    await this.scheduleAppointmentsForRecurring(id);

    // Create audit log
    const oldValues = {
      startDate: existingRecurring.startDate,
      endDate: existingRecurring.endDate,
      duration: existingRecurring.duration,
      type: existingRecurring.type,
      frequency: existingRecurring.frequency,
    };
    const newValues = {
      startDate: updatedRecurring.startDate,
      endDate: updatedRecurring.endDate,
      duration: updatedRecurring.duration,
      type: updatedRecurring.type,
      frequency: updatedRecurring.frequency,
    };

    await this.createAuditLog({
      userId,
      action: 'UPDATE',
      entityType: 'RECURRING_APPOINTMENT',
      entityId: id,
      patientId: existingRecurring.patientId,
      oldValues,
      newValues,
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: { recurringAppointment: updatedRecurring },
      message: 'Recurring appointment updated successfully',
    };
  }

  /**
   * Cancel recurring appointment series
   */
  static async cancelRecurringAppointment(
    id: string,
    reason: string,
    cancelFutureAppointments: boolean,
    userId: string,
    userRole: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ cancelledAppointments: number }>> {
    // Check if recurring appointment exists and user has access
    const recurringAppointment = await prisma.recurringAppointment.findFirst({
      where: {
        id,
        ...(userRole === 'CLINICIAN' && { providerId: userId }),
      },
    });

    if (!recurringAppointment) {
      throw new NotFoundError('Recurring appointment not found');
    }

    // Deactivate recurring appointment
    await prisma.recurringAppointment.update({
      where: { id },
      data: {
        isActive: false,
        notes: recurringAppointment.notes 
          ? `${recurringAppointment.notes}\n\nSeries cancelled: ${reason}`
          : `Series cancelled: ${reason}`,
      },
    });

    let cancelledCount = 0;

    // Cancel future appointments if requested
    if (cancelFutureAppointments) {
      const futureAppointments = await prisma.appointment.findMany({
        where: {
          recurringAppointmentId: id,
          date: { gte: new Date() },
          status: { in: ['SCHEDULED', 'CONFIRMED'] },
          isDeleted: false,
        },
      });

      for (const appointment of futureAppointments) {
        await AppointmentService.cancelAppointment(
          appointment.id,
          `Recurring series cancelled: ${reason}`,
          userId,
          userRole,
          auditData
        );
        cancelledCount++;
      }
    }

    // Create audit log
    await this.createAuditLog({
      userId,
      action: 'CANCEL',
      entityType: 'RECURRING_APPOINTMENT',
      entityId: id,
      patientId: recurringAppointment.patientId,
      newValues: {
        reason,
        cancelledAppointments: cancelledCount,
      },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: { cancelledAppointments: cancelledCount },
      message: `Recurring appointment series cancelled. ${cancelledCount} future appointments cancelled.`,
    };
  }

  /**
   * Get recurring appointments
   */
  static async getRecurringAppointments(
    query: {
      page?: string;
      limit?: string;
      patientId?: string;
      providerId?: string;
      isActive?: string;
    },
    userId: string,
    userRole: string
  ): Promise<any> {
    const page = parseInt(query.page || '1', 10);
    const limit = Math.min(parseInt(query.limit || '10', 10), 100);
    const skip = (page - 1) * limit;

    const where: any = {};

    // Role-based filtering
    if (userRole === 'CLINICIAN') {
      where.providerId = userId;
    }

    if (query.patientId) where.patientId = query.patientId;
    if (query.providerId) where.providerId = query.providerId;
    if (query.isActive !== undefined) where.isActive = query.isActive === 'true';

    const [recurringAppointments, total] = await Promise.all([
      prisma.recurringAppointment.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          provider: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
          appointments: {
            where: { isDeleted: false },
            select: {
              id: true,
              date: true,
              status: true,
            },
            orderBy: { date: 'asc' },
          },
        },
      }),
      prisma.recurringAppointment.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: recurringAppointments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Create audit log entry
   */
  private static async createAuditLog(data: AuditLogData): Promise<void> {
    const { oldValues, newValues, ...rest } = data;

    await prisma.auditLog.create({
      data: {
        ...rest,
        oldValues: oldValues ? JSON.stringify(oldValues) : null,
        newValues: newValues ? JSON.stringify(newValues) : null,
      },
    });
  }

  /**
   * Get recurring appointment by ID
   */
  static async getRecurringAppointmentById(id: string): Promise<ApiResponse<{ recurringAppointment: any }>> {
    const recurringAppointment = await prisma.recurringAppointment.findUnique({
      where: { id },
      include: {
        patient: {
          select: { id: true, firstName: true, lastName: true },
        },
        provider: {
          select: { id: true, firstName: true, lastName: true },
        },
        appointments: {
          orderBy: { date: 'asc' },
          take: 5, // Get next 5 upcoming appointments
        },
      },
    });

    if (!recurringAppointment) {
      throw new NotFoundError('Recurring appointment not found');
    }

    return {
      success: true,
      data: { recurringAppointment },
    };
  }

  /**
   * Get all recurring appointments for a patient
   */
  static async getRecurringAppointmentsByPatient(patientId: string): Promise<ApiResponse<{ recurringAppointments: any[] }>> {
    const recurringAppointments = await prisma.recurringAppointment.findMany({
      where: { patientId, isDeleted: false },
      include: {
        provider: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
      orderBy: { startDate: 'desc' },
    });

    return {
      success: true,
      data: { recurringAppointments },
    };
  }

  /**
   * Delete a recurring appointment
   */
  static async deleteRecurringAppointment(id: string, userId: string, auditData?: { ipAddress?: string; userAgent?: string }): Promise<ApiResponse<null>> {
    const recurring = await prisma.recurringAppointment.findUnique({ where: { id } });
    if (!recurring) {
      throw new NotFoundError('Recurring appointment not found');
    }

    await prisma.recurringAppointment.update({
      where: { id },
      data: { isDeleted: true, isActive: false, deletedAt: new Date() },
    });

    // Cancel future appointments
    await prisma.appointment.updateMany({
      where: {
        recurringAppointmentId: id,
        date: { gte: new Date() },
        status: { in: ['SCHEDULED', 'CONFIRMED'] },
      },
      data: {
        status: 'CANCELLED',
        notes: 'Parent recurring appointment was deleted.',
      },
    });

    // Create audit log
    await this.createAuditLog({
      userId,
      action: 'DELETE',
      entityType: 'RECURRING_APPOINTMENT',
      entityId: id,
      patientId: recurring.patientId,
      oldValues: {
        isActive: recurring.isActive,
        isDeleted: recurring.isDeleted,
      },
      newValues: {
        isActive: false,
        isDeleted: true,
      },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      message: 'Recurring appointment deleted successfully',
    };
  }
}
