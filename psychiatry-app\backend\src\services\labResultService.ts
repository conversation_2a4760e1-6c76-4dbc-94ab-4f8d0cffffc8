import { PrismaClient } from '@prisma/client';
import { 
  CreateLabResultData, 
  ApiResponse, 
  PaginatedResponse,
  AuditLogData 
} from '@/types';
import { NotFoundError, ValidationError, AuthorizationError } from '@/utils/errors';

const prisma = new PrismaClient();

/**
 * Lab Result service handling all lab result operations
 * Includes CRUD operations, trend analysis, and normal range checking
 */
export class LabResultService {
  /**
   * Create a new lab result
   */
  static async createLabResult(
    data: CreateLabResultData,
    createdBy: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ labResult: any }>> {
    // Validate patient exists and user has access
    const patient = await prisma.patient.findFirst({
      where: {
        id: data.patientId,
        isDeleted: false,
      },
    });

    if (!patient) {
      throw new NotFoundError('Patient not found');
    }

    // Validate required fields
    if (!data.testType || !data.testDate || !data.orderedBy || !data.results) {
      throw new ValidationError('Test type, test date, ordered by, and results are required');
    }

    // Validate test date
    const testDate = new Date(data.testDate);
    if (isNaN(testDate.getTime()) || testDate > new Date()) {
      throw new ValidationError('Invalid test date');
    }

    // Create lab result
    const labResult = await prisma.labResult.create({
      data: {
        patientId: data.patientId,
        testType: data.testType,
        testDate: testDate,
        orderedBy: data.orderedBy.trim(),
        labName: data.labName?.trim() || null,
        results: JSON.stringify(data.results),
        normalRanges: data.normalRanges ? JSON.stringify(data.normalRanges) : null,
        flags: data.flags ? JSON.stringify(data.flags) : null,
        notes: data.notes?.trim() || null,
        status: data.status || 'COMPLETED',
        createdBy,
      },
      include: {
        patient: {
          select: {
            id: true,
            patientId: true,
            firstName: true,
            lastName: true,
          },
        },
        // creator: {
        //   select: {
        //     id: true,
        //     firstName: true,
        //     lastName: true,
        //     username: true,
        //   },
        // },
      },
    });

    // Create audit log
    await this.createAuditLog({
      userId: createdBy,
      action: 'CREATE',
      entityType: 'LAB_RESULT',
      entityId: labResult.id,
      patientId: data.patientId,
      labResultId: labResult.id,
      newValues: {
        testType: labResult.testType,
        testDate: labResult.testDate,
        orderedBy: labResult.orderedBy,
        status: labResult.status,
      },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: { labResult },
      message: 'Lab result created successfully',
    };
  }

  /**
   * Get lab results with pagination and filtering
   */
  static async getLabResults(
    query: {
      page?: string;
      limit?: string;
      patientId?: string;
      testType?: string;
      status?: string;
      dateFrom?: string;
      dateTo?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    },
    userId: string,
    userRole: string
  ): Promise<PaginatedResponse<any>> {
    const page = parseInt(query.page || '1', 10);
    const limit = Math.min(parseInt(query.limit || '10', 10), 100);
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      isDeleted: false,
    };

    // Role-based filtering: non-admins can only see results for their patients
    if (userRole !== 'ADMIN') {
      where.patient = {
        createdBy: userId,
      };
    }

    // Patient filter
    if (query.patientId) {
      where.patientId = query.patientId;
    }

    // Test type filter
    if (query.testType) {
      where.testType = query.testType;
    }

    // Status filter
    if (query.status) {
      where.status = query.status;
    }

    // Date range filter
    if (query.dateFrom || query.dateTo) {
      where.testDate = {};
      if (query.dateFrom) {
        where.testDate.gte = new Date(query.dateFrom);
      }
      if (query.dateTo) {
        where.testDate.lte = new Date(query.dateTo);
      }
    }

    // Sorting
    const orderBy: any = {};
    if (query.sortBy) {
      const direction = query.sortOrder === 'desc' ? 'desc' : 'asc';
      orderBy[query.sortBy] = direction;
    } else {
      orderBy.testDate = 'desc'; // Default sort by test date
    }

    // Execute queries
    const [labResults, total] = await Promise.all([
      prisma.labResult.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
        },
      }),
      prisma.labResult.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: labResults,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Get lab result by ID
   */
  static async getLabResultById(
    id: string,
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ labResult: any }>> {
    const labResult = await prisma.labResult.findFirst({
      where: {
        id,
        isDeleted: false,
        ...(userRole !== 'ADMIN' && {
          patient: {
            createdBy: userId,
          },
        }),
      },
      include: {
        patient: {
          select: {
            id: true,
            patientId: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
          },
        },
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          },
        },
      },
    });

    if (!labResult) {
      throw new NotFoundError('Lab result not found');
    }

    // Create audit log for viewing
    await this.createAuditLog({
      userId,
      action: 'VIEW',
      entityType: 'LAB_RESULT',
      entityId: labResult.id,
      patientId: labResult.patientId,
      labResultId: labResult.id,
    });

    return {
      success: true,
      data: { labResult },
    };
  }

  /**
   * Get lab results for a specific patient
   */
  static async getPatientLabResults(
    patientId: string,
    userId: string,
    userRole: string,
    query: {
      testType?: string;
      limit?: number;
      dateFrom?: string;
      dateTo?: string;
    } = {}
  ): Promise<ApiResponse<{ labResults: any[] }>> {
    // Check if patient exists and user has access
    const patient = await prisma.patient.findFirst({
      where: {
        id: patientId,
        isDeleted: false,
        ...(userRole !== 'ADMIN' && { createdBy: userId }),
      },
    });

    if (!patient) {
      throw new NotFoundError('Patient not found');
    }

    const where: any = {
      patientId,
      isDeleted: false,
    };

    if (query.testType) {
      where.testType = query.testType;
    }

    if (query.dateFrom || query.dateTo) {
      where.testDate = {};
      if (query.dateFrom) {
        where.testDate.gte = new Date(query.dateFrom);
      }
      if (query.dateTo) {
        where.testDate.lte = new Date(query.dateTo);
      }
    }

    const labResults = await prisma.labResult.findMany({
      where,
      take: query.limit || 50,
      orderBy: { testDate: 'desc' },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          },
        },
      },
    });

    return {
      success: true,
      data: { labResults },
    };
  }

  /**
   * Get lab result trends for a patient
   */
  static async getLabResultTrends(
    patientId: string,
    testType: string,
    userId: string,
    userRole: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ApiResponse<{ trends: any[] }>> {
    // Check if patient exists and user has access
    const patient = await prisma.patient.findFirst({
      where: {
        id: patientId,
        isDeleted: false,
        ...(userRole !== 'ADMIN' && { createdBy: userId }),
      },
    });

    if (!patient) {
      throw new NotFoundError('Patient not found');
    }

    const where: any = {
      patientId,
      testType,
      isDeleted: false,
      status: 'COMPLETED',
    };

    if (dateFrom || dateTo) {
      where.testDate = {};
      if (dateFrom) {
        where.testDate.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.testDate.lte = new Date(dateTo);
      }
    }

    const labResults = await prisma.labResult.findMany({
      where,
      orderBy: { testDate: 'asc' },
      select: {
        id: true,
        testDate: true,
        results: true,
        normalRanges: true,
        flags: true,
      },
    });

    return {
      success: true,
      data: { trends: labResults },
    };
  }

  /**
   * Update lab result
   */
  static async updateLabResult(
    id: string,
    data: Partial<CreateLabResultData>,
    updatedBy: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ labResult: any }>> {
    // Check if lab result exists and user has access
    const existingLabResult = await prisma.labResult.findFirst({
      where: {
        id,
        isDeleted: false,
      },
      include: {
        patient: {
          select: {
            patientId: true,
            firstName: true,
            lastName: true,
            createdBy: true,
          },
        },
      },
    });

    if (!existingLabResult) {
      throw new NotFoundError('Lab result not found');
    }

    // Check authorization
    const user = await prisma.user.findUnique({
      where: { id: updatedBy },
      select: { role: true },
    });

    if (!user) {
      throw new AuthorizationError('User not found');
    }

    if (user.role !== 'ADMIN' && existingLabResult.patient.createdBy !== updatedBy) {
      throw new AuthorizationError('Not authorized to update this lab result');
    }

    // Update lab result
    const updatedLabResult = await prisma.labResult.update({
      where: { id },
      data: {
        ...(data.testType && { testType: data.testType }),
        ...(data.testDate && { testDate: new Date(data.testDate) }),
        ...(data.orderedBy && { orderedBy: data.orderedBy }),
        ...(data.labName !== undefined && { labName: data.labName }),
        ...(data.results && { results: data.results }),
        ...(data.normalRanges !== undefined && { normalRanges: data.normalRanges }),
        ...(data.flags !== undefined && { flags: data.flags }),
        ...(data.notes !== undefined && { notes: data.notes }),
        ...(data.status && { status: data.status }),
      },
      include: {
        patient: {
          select: {
            patientId: true,
            firstName: true,
            lastName: true,
          },
        },
        creator: {
          select: {
            firstName: true,
            lastName: true,
            username: true,
          },
        },
      },
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        userId: updatedBy,
        action: 'UPDATE',
        entityType: 'LAB_RESULT',
        entityId: id,
        oldValues: JSON.stringify({
          testType: existingLabResult.testType,
          testDate: existingLabResult.testDate,
          orderedBy: existingLabResult.orderedBy,
          labName: existingLabResult.labName,
          results: existingLabResult.results,
          normalRanges: existingLabResult.normalRanges,
          flags: existingLabResult.flags,
          notes: existingLabResult.notes,
          status: existingLabResult.status,
        }),
        newValues: JSON.stringify({
          testType: updatedLabResult.testType,
          testDate: updatedLabResult.testDate,
          orderedBy: updatedLabResult.orderedBy,
          labName: updatedLabResult.labName,
          results: updatedLabResult.results,
          normalRanges: updatedLabResult.normalRanges,
          flags: updatedLabResult.flags,
          notes: updatedLabResult.notes,
          status: updatedLabResult.status,
        }),
        patientId: existingLabResult.patientId,
        labResultId: id,
        ipAddress: auditData?.ipAddress,
        userAgent: auditData?.userAgent,
      },
    });

    return {
      success: true,
      data: { labResult: updatedLabResult },
    };
  }

  /**
   * Delete lab result (soft delete)
   */
  static async deleteLabResult(
    id: string,
    userId: string,
    userRole: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<null>> {
    // Check if lab result exists and user has access
    const labResult = await prisma.labResult.findFirst({
      where: {
        id,
        isDeleted: false,
        ...(userRole !== 'ADMIN' && {
          patient: {
            createdBy: userId,
          },
        }),
      },
      include: {
        patient: {
          select: {
            patientId: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!labResult) {
      throw new NotFoundError('Lab result not found');
    }

    // Soft delete lab result
    await prisma.labResult.update({
      where: { id },
      data: {
        isDeleted: true,
        deletedAt: new Date(),
      },
    });

    // Create audit log
    await this.createAuditLog({
      userId,
      action: 'DELETE',
      entityType: 'LAB_RESULT',
      entityId: id,
      patientId: labResult.patientId,
      labResultId: id,
      oldValues: {
        testType: labResult.testType,
        testDate: labResult.testDate,
        patient: `${labResult.patient.firstName} ${labResult.patient.lastName}`,
      },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: null,
      message: 'Lab result deleted successfully',
    };
  }

  /**
   * Create audit log entry
   */
  private static async createAuditLog(data: AuditLogData): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          entityType: data.entityType,
          entityId: data.entityId,
          oldValues: data.oldValues ? JSON.stringify(data.oldValues) : null,
          newValues: data.newValues ? JSON.stringify(data.newValues) : null,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          patientId: data.patientId,
          labResultId: data.labResultId,
          appointmentId: data.appointmentId,
        },
      });
    } catch (error) {
      console.error('Failed to create audit log:', error);
    }
  }
}
