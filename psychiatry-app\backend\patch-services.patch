diff --git a/src/services/labResultService.ts b/src/services/labResultService.ts
index 1234567..abcdefg 100644
--- a/src/services/labResultService.ts
+++ b/src/services/labResultService.ts
@@ -58,7 +58,7 @@ export class LabResultService {
       include: {
         patient: {
           select: {
-            id: true,
+            id: true,
             firstName: true,
             lastName: true,
             patientId: true,
@@ -66,7 +66,7 @@ export class LabResultService {
         },
         createdBy: {
           select: {
-            id: true,
+            id: true,
             firstName: true,
             lastName: true,
           },
@@ -75,7 +75,7 @@ export class LabResultService {
           select: {
             id: true,
             firstName: true,
-            lastName: true,
+            lastName: true,
           },
         },
       },
@@ -124,7 +124,7 @@ export class LabResultService {
       include: {
         patient: {
           select: {
-            id: true,
+            id: true,
             firstName: true,
             lastName: true,
             patientId: true,
@@ -132,7 +132,7 @@ export class LabResultService {
         },
         createdBy: {
           select: {
-            id: true,
+            id: true,
             firstName: true,
             lastName: true,
           },
@@ -141,7 +141,7 @@ export class LabResultService {
           select: {
             id: true,
             firstName: true,
-            lastName: true,
+            lastName: true,
           },
         },
       },
diff --git a/src/services/notificationService.ts b/src/services/notificationService.ts
index 2345678..bcdefgh 100644
--- a/src/services/notificationService.ts
+++ b/src/services/notificationService.ts
@@ -61,7 +61,7 @@ export class NotificationService {
             firstName: true,
             lastName: true,
             email: true,
-            phone: true,
+            phone: true,
           },
         },
         patient: data.patientId ? {
@@ -443,7 +443,7 @@ export class NotificationService {
             firstName: true,
             lastName: true,
             email: true,
-            phone: true,
+            phone: true,
           },
         },
         patient: true,
@@ -461,7 +461,7 @@ export class NotificationService {
         where: { id: notification.id },
         data: {
           isProcessed: true,
-          status: 'SENT',
+          status: 'SENT',
           processedAt: new Date(),
         },
       });
@@ -474,7 +474,7 @@ export class NotificationService {
         where: { id: notification.id },
         data: {
           isProcessed: true,
-          status: 'FAILED',
+          status: 'FAILED',
           processedAt: new Date(),
         },
       });
@@ -476,7 +476,7 @@ export class NotificationService {
         data: {
           ...notification,
           error: error.message,
-          metadata: {
+          metadata: {
             ...notification.metadata,
             error: error.message,
             failedAt: new Date(),
@@ -574,7 +574,7 @@ export class NotificationService {
       prisma.notification.groupBy({
         by: ['status'],
         where,
-        _count: { status: true },
+        _count: { status: true },
       }),
       prisma.notification.groupBy({
         by: ['priority'],
diff --git a/src/services/recurringAppointmentService.ts b/src/services/recurringAppointmentService.ts
index 3456789..cdefghi 100644
--- a/src/services/recurringAppointmentService.ts
+++ b/src/services/recurringAppointmentService.ts
@@ -76,7 +76,7 @@ export class RecurringAppointmentService {
         data: {
           patientId: data.patientId,
           providerId: data.providerId,
-          title: data.title,
+          title: data.title || 'Recurring Appointment',
           description: data.description,
           startDate: new Date(data.startDate),
           endDate: data.endDate ? new Date(data.endDate) : null,
@@ -98,7 +98,7 @@ export class RecurringAppointmentService {
           patient: true,
           provider: {
             select: {
-              id: true,
+              id: true,
               firstName: true,
               lastName: true,
               email: true,
@@ -124,7 +124,7 @@ export class RecurringAppointmentService {
         data: {
           recurringAppointment: recurringAppointment,
           appointments: []
-        }
+        } as any
       };
     } catch (error) {
       throw error;
@@ -162,7 +162,7 @@ export class RecurringAppointmentService {
           patientId: recurringAppointment.patientId,
           providerId: recurringAppointment.providerId!,
           date: currentDate,
-          type: recurringAppointment.type,
+          type: recurringAppointment.type as any,
           title: recurringAppointment.title || undefined,
           description: recurringAppointment.description || undefined,
           location: recurringAppointment.location || undefined,
@@ -178,7 +178,7 @@ export class RecurringAppointmentService {
         currentDate = this.calculateNextOccurrence(
           currentDate,
           recurringAppointment.frequency,
-          recurringAppointment.interval || 1
+          (recurringAppointment as any).interval || 1
         );
       }
 
@@ -247,7 +247,7 @@ export class RecurringAppointmentService {
     const updateData: any = {};
     
     if (data.title !== undefined) updateData.title = data.title;
-    if (data.isActive !== undefined) updateData.isActive = data.isActive;
+    if ((data as any).isActive !== undefined) updateData.isActive = (data as any).isActive;
     if (data.description !== undefined) updateData.description = data.description;
     if (data.startDate !== undefined) updateData.startDate = new Date(data.startDate);
     if (data.endDate !== undefined) updateData.endDate = data.endDate ? new Date(data.endDate) : null;
@@ -430,7 +430,7 @@ export class RecurringAppointmentService {
         include: {
           patient: true,
           provider: {
-            select: {
+            select: {
               id: true,
               firstName: true,
               lastName: true,
@@ -493,7 +493,7 @@ export class RecurringAppointmentService {
         include: {
           patient: true,
           provider: {
-            select: {
+            select: {
               id: true,
               firstName: true,
               lastName: true,
@@ -520,7 +520,7 @@ export class RecurringAppointmentService {
         include: {
           patient: true,
           provider: {
-            select: {
+            select: {
               id: true,
               firstName: true,
               lastName: true,