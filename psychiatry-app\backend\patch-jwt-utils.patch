diff --git a/src/utils/jwt.ts b/src/utils/jwt.ts
index 1234567..abcdefg 100644
--- a/src/utils/jwt.ts
+++ b/src/utils/jwt.ts
@@ -23,7 +23,7 @@ export const generateTokens = (userId: string, role: string): TokenPair => {
   const payload = { userId, role };
   
   const accessToken = jwt.sign(payload, JWT_SECRET, {
-    expiresIn: JWT_EXPIRES_IN || '15m',
+    expiresIn: (JWT_EXPIRES_IN as any) || '15m',
   });
   
   const refreshToken = jwt.sign(payload, JWT_REFRESH_SECRET, {
@@ -31,7 +31,7 @@ export const generateTokens = (userId: string, role: string): TokenPair => {
   });
   
   return {
-    accessToken,
-    refreshToken,
+    accessToken: accessToken as string,
+    refreshToken: refreshToken as string,
   };
 };