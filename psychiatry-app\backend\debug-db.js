const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugDatabase() {
  try {
    console.log('🔍 Debugging Database Issues...\n');
    
    // Check patients
    const patients = await prisma.patient.findMany({
      where: { isDeleted: false },
      select: { id: true, patientId: true, firstName: true, lastName: true, createdAt: true },
      orderBy: { createdAt: 'desc' },
      take: 10
    });
    
    console.log('📋 Patients:', patients);
    console.log(`Total patients: ${patients.length}\n`);
    
    // Check users
    const users = await prisma.user.findMany({
      where: { isActive: true },
      select: { id: true, username: true, role: true, firstName: true, lastName: true },
      take: 5
    });
    
    console.log('👥 Users:', users);
    console.log(`Total active users: ${users.length}\n`);
    
    // Check appointments
    const appointments = await prisma.appointment.findMany({
      where: { isDeleted: false },
      select: { id: true, patientId: true, date: true, status: true },
      take: 5
    });
    
    console.log('📅 Appointments:', appointments);
    console.log(`Total appointments: ${appointments.length}\n`);
    
    // Check lab results
    const labResults = await prisma.labResult.findMany({
      where: { isDeleted: false },
      select: { id: true, patientId: true, testType: true, testDate: true, status: true },
      take: 5
    });
    
    console.log('🧪 Lab Results:', labResults);
    console.log(`Total lab results: ${labResults.length}\n`);
    
    // Check patient assessments
    const assessments = await prisma.patientAssessment.findMany({
      where: { isDeleted: false },
      select: { id: true, patientId: true, sessionDate: true, status: true },
      take: 5
    });
    
    console.log('📝 Patient Assessments:', assessments);
    console.log(`Total assessments: ${assessments.length}\n`);
    
  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugDatabase();