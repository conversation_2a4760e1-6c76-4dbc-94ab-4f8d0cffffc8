"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const client_1 = require("@prisma/client");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
const prisma = new client_1.PrismaClient();
app.use((0, cors_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', async (req, res) => {
    try {
        await prisma.$queryRaw `SELECT 1`;
        res.status(200).json({
            success: true,
            message: 'Server is healthy',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV,
            database: 'connected'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Database connection failed',
            error: error.message
        });
    }
});
app.get('/api/stats', async (req, res) => {
    try {
        const stats = {
            users: await prisma.user.count(),
            patients: await prisma.patient.count(),
            labResults: await prisma.labResult.count(),
            appointments: await prisma.appointment.count(),
            notifications: await prisma.notification.count(),
            recurringAppointments: await prisma.recurringAppointment.count(),
            auditLogs: 0
        };
        res.status(200).json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch stats',
            error: error.message
        });
    }
});
app.get('/api/users', async (req, res) => {
    try {
        const users = await prisma.user.findMany({
            select: {
                id: true,
                username: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                isActive: true,
                createdAt: true
            }
        });
        res.status(200).json({
            success: true,
            data: users
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch users',
            error: error.message
        });
    }
});
app.post('/api/users', async (req, res) => {
    try {
        const { username, email, password, firstName, lastName, role = 'CLINICIAN' } = req.body;
        if (!username || !email || !password || !firstName || !lastName) {
            res.status(400).json({
                success: false,
                message: 'Missing required fields'
            });
            return;
        }
        const user = await prisma.user.create({
            data: {
                username,
                email,
                password,
                firstName,
                lastName,
                role
            },
            select: {
                id: true,
                username: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                isActive: true,
                createdAt: true
            }
        });
        res.status(201).json({
            success: true,
            data: user,
            message: 'User created successfully'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to create user',
            error: error.message
        });
    }
});
app.get('/api/patients', async (req, res) => {
    try {
        const patients = await prisma.patient.findMany({
            where: {
                isDeleted: false
            },
            select: {
                id: true,
                patientId: true,
                firstName: true,
                lastName: true,
                dateOfBirth: true,
                gender: true,
                phone: true,
                email: true,
                isActive: true,
                createdAt: true
            }
        });
        res.status(200).json({
            success: true,
            data: patients
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch patients',
            error: error.message
        });
    }
});
app.post('/api/patients', async (req, res) => {
    try {
        const { firstName, lastName, dateOfBirth, gender, phone, email, createdBy = 'system' } = req.body;
        if (!firstName || !lastName || !dateOfBirth || !gender) {
            res.status(400).json({
                success: false,
                message: 'Missing required fields'
            });
            return;
        }
        const patientCount = await prisma.patient.count();
        const patientId = `P-${new Date().getFullYear()}-${String(patientCount + 1).padStart(3, '0')}`;
        const patient = await prisma.patient.create({
            data: {
                patientId,
                firstName,
                lastName,
                dateOfBirth: new Date(dateOfBirth),
                gender,
                phone: phone || null,
                email: email || null,
                createdBy
            },
            select: {
                id: true,
                patientId: true,
                firstName: true,
                lastName: true,
                dateOfBirth: true,
                gender: true,
                phone: true,
                email: true,
                isActive: true,
                createdAt: true
            }
        });
        res.status(201).json({
            success: true,
            data: patient,
            message: 'Patient created successfully'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to create patient',
            error: error.message
        });
    }
});
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found',
        path: req.originalUrl
    });
});
app.use((error, req, res, next) => {
    console.error('Error:', error);
    res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
});
const server = app.listen(PORT, () => {
    console.log(`🚀 Minimal server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 Health Check: http://localhost:${PORT}/health`);
    console.log(`🔗 API Stats: http://localhost:${PORT}/api/stats`);
});
process.on('SIGTERM', async () => {
    console.log('SIGTERM received, shutting down gracefully');
    await prisma.$disconnect();
    server.close(() => {
        console.log('Process terminated');
    });
});
process.on('SIGINT', async () => {
    console.log('SIGINT received, shutting down gracefully');
    await prisma.$disconnect();
    server.close(() => {
        console.log('Process terminated');
    });
});
exports.default = app;
//# sourceMappingURL=minimal-server.js.map