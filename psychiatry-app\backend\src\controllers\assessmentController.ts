import { Response, NextFunction } from 'express';
import { z } from 'zod';
import { AuthRequest } from '@/types';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Validation schemas
const criterionSchema = z.object({
  id: z.string(),
  code: z.string(),
  description: z.string(),
  present: z.boolean(),
  severity: z.number().optional(),
  duration: z.string().optional(),
  onset: z.string().optional(),
  comments: z.string().optional(),
  subCriteria: z.array(z.lazy(() => criterionSchema)).optional(),
});

const specifierSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  selected: z.boolean(),
  mutuallyExclusive: z.array(z.string()).optional(),
});

const disorderAssessmentSchema = z.object({
  id: z.string().optional(),
  disorderId: z.string(),
  patientId: z.string(),
  assessmentDate: z.string(),
  criteria: z.array(criterionSchema),
  specifiers: z.array(specifierSchema).optional(),
  severity: z.enum(['mild', 'moderate', 'severe', 'unspecified']).optional(),
  courseSpecifiers: z.array(z.string()).optional(),
  diagnosticConfidence: z.enum(['provisional', 'principal', 'rule-out', 'confirmed']).optional(),
  functionalImpairment: z.object({
    social: z.number(),
    occupational: z.number(),
    academic: z.number(),
    overall: z.number(),
  }).optional(),
  notes: z.string().optional(),
  assessorId: z.string().optional(),
  status: z.enum(['draft', 'completed', 'reviewed']).optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

const riskAssessmentSchema = z.object({
  suicidalIdeation: z.boolean(),
  homicidalIdeation: z.boolean(),
  selfHarm: z.boolean(),
  substanceUse: z.boolean(),
  level: z.enum(['low', 'moderate', 'high', 'imminent']),
  interventions: z.array(z.string()).optional(),
});

const assessmentSessionSchema = z.object({
  patientId: z.string(),
  sessionDate: z.string(),
  assessments: z.array(disorderAssessmentSchema),
  clinicalImpression: z.string().optional(),
  treatmentRecommendations: z.array(z.string()).optional(),
  followUpPlan: z.string().optional(),
  riskAssessment: riskAssessmentSchema,
  status: z.enum(['in-progress', 'completed', 'reviewed', 'signed']),
  duration: z.number(),
  assessorId: z.string().optional(),
  supervisorId: z.string().optional(),
});

export class AssessmentController {
  /**
   * Create a new assessment session
   * POST /api/patients/:patientId/assessment-sessions
   */
  static async createAssessmentSession(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { patientId } = req.params;
      
      // Look up the patient by either formatted patientId or UUID
      const patient = await prisma.patient.findFirst({
        where: {
          OR: [
            { patientId },
            { id: patientId }
          ]
        }
      });
      
      if (!patient) {
        res.status(404).json({
          success: false,
          error: 'Patient not found',
        });
        return;
      }

      const validatedData = assessmentSessionSchema.parse({
        ...req.body,
        patientId: patient.id, // Use the actual UUID
      });
       
      // Store the assessment data in a JSON field for now
      // In a production environment, you would create proper tables for this data
      const result = await prisma.patientAssessment.create({
        data: {
          patientId: validatedData.patientId,
          assessorId: req.user.id,
          sessionDate: new Date(validatedData.sessionDate),
          assessmentData: JSON.stringify(validatedData),
          status: validatedData.status,
          duration: validatedData.duration,
        },
      });

      res.status(201).json({
        success: true,
        data: {
          id: result.id,
          ...validatedData,
          assessorId: req.user.id,
        },
        message: 'Assessment session created successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateAssessmentSession(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    // TODO: Implement update logic
    res.status(501).json({ success: false, error: "Not implemented yet" });
  }

  static async deleteAssessmentSession(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    // TODO: Implement delete logic
    res.status(501).json({ success: false, error: "Not implemented yet" });
  }

  /**
   * Get assessment sessions for a patient
   * GET /api/patients/:patientId/assessment-sessions
   */
  static async getPatientAssessmentSessions(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { patientId } = req.params;
      
      // Look up the patient by either formatted patientId or UUID
      const patient = await prisma.patient.findFirst({
        where: {
          OR: [
            { patientId },
            { id: patientId }
          ]
        }
      });
      
      if (!patient) {
        res.status(404).json({
          success: false,
          error: 'Patient not found',
        });
        return;
      }

      const assessments = await prisma.patientAssessment.findMany({
        where: {
          patientId: patient.id, // Use the actual UUID
          isDeleted: false,
        },
        orderBy: {
          sessionDate: 'desc',
        },
      });

      // Parse the JSON data for each assessment
      const parsedAssessments = assessments.map(assessment => ({
        id: assessment.id,
        ...JSON.parse(assessment.assessmentData),
        assessorId: assessment.assessorId,
        createdAt: assessment.createdAt,
        updatedAt: assessment.updatedAt,
      }));

      res.status(200).json({
        success: true,
        data: parsedAssessments,
        message: 'Assessment sessions retrieved successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get a specific assessment session
   * GET /api/assessment-sessions/:id
   */
  static async getAssessmentSessionById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;

      const assessment = await prisma.patientAssessment.findUnique({
        where: {
          id,
          isDeleted: false,
        },
      });

      if (!assessment) {
        res.status(404).json({
          success: false,
          error: 'Assessment session not found',
        });
        return;
      }

      // Parse the JSON data
      const parsedAssessment = {
        id: assessment.id,
        ...JSON.parse(assessment.assessmentData),
        assessorId: assessment.assessorId,
        createdAt: assessment.createdAt,
        updatedAt: assessment.updatedAt,
      };

      res.status(200).json({
        success: true,
        data: parsedAssessment,
        message: 'Assessment session retrieved successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
