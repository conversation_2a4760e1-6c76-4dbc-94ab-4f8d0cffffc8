import { PrismaClient } from '@prisma/client';
import { addDays, addHours, addMinutes, isBefore, isAfter } from 'date-fns';
import { 
  CreateNotificationData, 
  ApiResponse, 
  PaginatedResponse,
  AuditLogData 
} from '@/types';
import { NotFoundError, ValidationError } from '@/utils/errors';

const prisma = new PrismaClient();

/**
 * Notification service handling email/SMS notifications and reminders
 * Includes appointment reminders, status updates, and system notifications
 */
export class NotificationService {
  /**
   * Create a new notification
   */
  static async createNotification(
    data: CreateNotificationData,
    createdBy: string
  ): Promise<ApiResponse<{ notification: any }>> {
    // Validate required fields
    if (!data.recipientId || !data.type || !data.title || !data.message) {
      throw new ValidationError('Recipient ID, type, title, and message are required');
    }

    // Validate recipient exists
    const recipient = await prisma.user.findFirst({
      where: {
        id: data.recipientId,
        isActive: true,
      },
    });

    if (!recipient) {
      throw new NotFoundError('Recipient not found');
    }

    // Create notification
    const notification = await prisma.notification.create({
      data: {
        recipientId: data.recipientId,
        type: data.type,
        title: data.title.trim(),
        message: data.message.trim(),
        priority: data.priority || 'MEDIUM',
        channel: data.channel || 'IN_APP',
        scheduledFor: data.scheduledFor ? new Date(data.scheduledFor) : new Date(),
        metadata: data.metadata || null,
        patientId: data.patientId || null,
        appointmentId: data.appointmentId || null,
        labResultId: data.labResultId || null,
        createdBy,
      },
      include: {
        recipient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        },
        patient: data.patientId ? {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            patientId: true,
          },
        } : false,
        appointment: data.appointmentId ? {
          select: {
            id: true,
            date: true,
            type: true,
            status: true,
          },
        } : false,
      },
    });

    return {
      success: true,
      data: { notification },
      message: 'Notification created successfully',
    };
  }

  /**
   * Get notifications with pagination and filtering
   */
  static async getNotifications(
    query: {
      page?: string;
      limit?: string;
      recipientId?: string;
      type?: string;
      status?: string;
      priority?: string;
      channel?: string;
      dateFrom?: string;
      dateTo?: string;
    },
    userId: string,
    userRole: string
  ): Promise<PaginatedResponse<any>> {
    const page = parseInt(query.page || '1', 10);
    const limit = Math.min(parseInt(query.limit || '10', 10), 100);
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    // Role-based filtering: non-admins can only see their own notifications
    if (userRole !== 'ADMIN') {
      where.recipientId = userId;
    }

    // Recipient filter (admin only)
    if (query.recipientId && userRole === 'ADMIN') {
      where.recipientId = query.recipientId;
    }

    // Type filter
    if (query.type) {
      where.type = query.type;
    }

    // Status filter
    if (query.status) {
      where.status = query.status;
    }

    // Priority filter
    if (query.priority) {
      where.priority = query.priority;
    }

    // Channel filter
    if (query.channel) {
      where.channel = query.channel;
    }

    // Date range filter
    if (query.dateFrom || query.dateTo) {
      where.createdAt = {};
      if (query.dateFrom) {
        where.createdAt.gte = new Date(query.dateFrom);
      }
      if (query.dateTo) {
        where.createdAt.lte = new Date(query.dateTo);
      }
    }

    // Execute queries
    const [notifications, total] = await Promise.all([
      prisma.notification.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          recipient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              patientId: true,
            },
          },
          appointment: {
            select: {
              id: true,
              date: true,
              type: true,
              status: true,
            },
          },
        },
      }),
      prisma.notification.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: notifications,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(
    id: string,
    userId: string,
    userRole: string
  ): Promise<ApiResponse<null>> {
    const where: any = {
      id,
    };

    // Non-admins can only mark their own notifications as read
    if (userRole !== 'ADMIN') {
      where.recipientId = userId;
    }

    const notification = await prisma.notification.findFirst({ where });

    if (!notification) {
      throw new NotFoundError('Notification not found');
    }

    await prisma.notification.update({
      where: { id },
      data: {
        isRead: true,
        status: 'READ',
      },
    });

    return {
      success: true,
      data: null,
      message: 'Notification marked as read',
    };
  }

  /**
   * Get notification by ID
   */
  static async getNotificationById(
    id: string,
    userId: string
  ): Promise<ApiResponse<any>> {
    const notification = await prisma.notification.findFirst({
      where: {
        id,
        recipientId: userId, // Users can only access their own notifications
      },
      include: {
        patient: {
          select: {
            patientId: true,
            firstName: true,
            lastName: true,
          },
        },
        appointment: {
          select: {
            id: true,
            date: true,
            type: true,
          },
        },
        labResult: {
          select: {
            id: true,
            testType: true,
            testDate: true,
          },
        },
      },
    });

    if (!notification) {
      throw new NotFoundError('Notification not found');
    }

    return {
      success: true,
      data: notification,
    };
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<ApiResponse<any>> {
    const result = await prisma.notification.updateMany({
      where: {
        recipientId: userId,
        isRead: false,
      },
      data: {
        isRead: true,
        status: 'READ',
      },
    });

    return {
      success: true,
      data: { updatedCount: result.count },
      message: `Marked ${result.count} notifications as read`,
    };
  }

  /**
   * Delete notification
   */
  static async deleteNotification(
    id: string,
    userId: string
  ): Promise<ApiResponse<any>> {
    const notification = await prisma.notification.findFirst({
      where: {
        id,
        recipientId: userId, // Users can only delete their own notifications
      },
    });

    if (!notification) {
      throw new NotFoundError('Notification not found');
    }

    await prisma.notification.delete({
      where: { id },
    });

    return {
      success: true,
      data: null,
      message: 'Notification deleted successfully',
    };
  }

  /**
   * Create appointment reminder notifications
   */
  static async createAppointmentReminders(
    appointmentId: string,
    createdBy: string
  ): Promise<ApiResponse<{ reminders: any[] }>> {
    // Get appointment details
    const appointment = await prisma.appointment.findFirst({
      where: {
        id: appointmentId,
        isDeleted: false,
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        },
        provider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!appointment) {
      throw new NotFoundError('Appointment not found');
    }

    const appointmentDate = new Date(appointment.date);
    const reminders = [];

    // Create reminder schedules
    const reminderSchedules = [
      { hours: 24, title: '24-Hour Reminder' },
      { hours: 2, title: '2-Hour Reminder' },
      { minutes: 15, title: '15-Minute Reminder' },
    ];

    for (const schedule of reminderSchedules) {
      let scheduledFor: Date;
      
      if (schedule.hours) {
        scheduledFor = addHours(appointmentDate, -schedule.hours);
      } else {
        scheduledFor = addMinutes(appointmentDate, -schedule.minutes!);
      }

      // Only create reminders for future times
      if (isAfter(scheduledFor, new Date())) {
        // Patient reminder
        const patientReminder = await this.createNotification({
          recipientId: appointment.patient.id,
          type: 'APPOINTMENT_REMINDER',
          title: `${schedule.title}: Upcoming Appointment`,
          message: `You have an appointment scheduled for ${appointmentDate.toLocaleString()} with Dr. ${appointment.provider.firstName} ${appointment.provider.lastName}.`,
          priority: schedule.minutes ? 'HIGH' : 'MEDIUM',
          channel: 'EMAIL',
          scheduledFor: scheduledFor.toISOString(),
          appointmentId: appointment.id,
          patientId: appointment.patient.id,
          metadata: {
            reminderType: schedule.hours ? `${schedule.hours}h` : `${schedule.minutes}m`,
            appointmentType: appointment.type,
          },
        }, createdBy);

        reminders.push(patientReminder.data?.notification);

        // Provider reminder (only for 2-hour and 15-minute)
        if (schedule.hours === 2 || schedule.minutes === 15) {
          const providerReminder = await this.createNotification({
            recipientId: appointment.provider.id,
            type: 'APPOINTMENT_REMINDER',
            title: `${schedule.title}: Patient Appointment`,
            message: `Upcoming appointment with ${appointment.patient.firstName} ${appointment.patient.lastName} at ${appointmentDate.toLocaleString()}.`,
            priority: schedule.minutes ? 'HIGH' : 'MEDIUM',
            channel: 'IN_APP',
            scheduledFor: scheduledFor.toISOString(),
            appointmentId: appointment.id,
            patientId: appointment.patient.id,
            metadata: {
              reminderType: schedule.hours ? `${schedule.hours}h` : `${schedule.minutes}m`,
              appointmentType: appointment.type,
            },
          }, createdBy);

          reminders.push(providerReminder.data?.notification);
        }
      }
    }

    return {
      success: true,
      data: { reminders },
      message: `Created ${reminders.length} appointment reminders`,
    };
  }

  /**
   * Send lab result notifications
   */
  static async sendLabResultNotification(
    labResultId: string,
    createdBy: string
  ): Promise<ApiResponse<{ notification: any }>> {
    // Get lab result details
    const labResult = await prisma.labResult.findFirst({
      where: {
        id: labResultId,
        isDeleted: false,
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!labResult) {
      throw new NotFoundError('Lab result not found');
    }

    // Determine priority based on flags
    let priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM';
    if (labResult.flags) {
      const flagValues = Object.values(labResult.flags);
      if (flagValues.some((f: any) => f.flag === 'CRITICAL')) {
        priority = 'CRITICAL';
      } else if (flagValues.some((f: any) => f.flag === 'HIGH' || f.flag === 'LOW')) {
        priority = 'HIGH';
      }
    }

    const notification = await this.createNotification({
      recipientId: labResult.patient.id,
      type: 'LAB_RESULT_AVAILABLE',
      title: 'New Lab Results Available',
      message: `Your ${labResult.testType} lab results from ${labResult.testDate.toLocaleDateString()} are now available for review.`,
      priority,
      channel: 'EMAIL',
      labResultId: labResult.id,
      patientId: labResult.patient.id,
      metadata: {
        testType: labResult.testType,
        hasFlags: !!labResult.flags && Object.keys(labResult.flags).length > 0,
        orderedBy: labResult.orderedBy,
      },
    }, createdBy);

    return notification;
  }

  /**
   * Process scheduled notifications (to be called by cron job)
   */
  static async processScheduledNotifications(): Promise<ApiResponse<{ processed: number }>> {
    const now = new Date();
    
    // Get notifications scheduled for now or earlier that haven't been sent
    const scheduledNotifications = await prisma.notification.findMany({
      where: {
        status: 'PENDING',
        scheduledFor: {
          lte: now,
        },
      },
      include: {
        recipient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        },
      },
      take: 100, // Process in batches
    });

    let processed = 0;

    for (const notification of scheduledNotifications) {
      try {
        // Simulate sending notification (in real implementation, integrate with email/SMS service)
        await this.sendNotification(notification);
        
        // Mark as sent
        await prisma.notification.update({
          where: { id: notification.id },
          data: {
            status: 'SENT',
            sentAt: new Date(),
          },
        });

        processed++;
      } catch (error) {
        console.error(`Failed to send notification ${notification.id}:`, error);
        
        // Mark as failed
        await prisma.notification.update({
          where: { id: notification.id },
          data: {
            status: 'FAILED',
            metadata: {
              ...notification.metadata,
              error: error instanceof Error ? error.message : 'Unknown error',
            },
          },
        });
      }
    }

    return {
      success: true,
      data: { processed },
      message: `Processed ${processed} scheduled notifications`,
    };
  }

  /**
   * Send notification via appropriate channel
   */
  private static async sendNotification(notification: any): Promise<void> {
    switch (notification.channel) {
      case 'EMAIL':
        await this.sendEmailNotification(notification);
        break;
      case 'SMS':
        await this.sendSMSNotification(notification);
        break;
      case 'IN_APP':
        // In-app notifications are already stored in database
        break;
      default:
        throw new Error(`Unsupported notification channel: ${notification.channel}`);
    }
  }

  /**
   * Send email notification (placeholder - integrate with email service)
   */
  private static async sendEmailNotification(notification: any): Promise<void> {
    // In a real implementation, integrate with services like:
    // - SendGrid
    // - AWS SES
    // - Mailgun
    // - Nodemailer with SMTP
    
    console.log(`Sending email to ${notification.recipient.email}:`);
    console.log(`Subject: ${notification.title}`);
    console.log(`Message: ${notification.message}`);
    
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Send SMS notification (placeholder - integrate with SMS service)
   */
  private static async sendSMSNotification(notification: any): Promise<void> {
    // In a real implementation, integrate with services like:
    // - Twilio
    // - AWS SNS
    // - Vonage (Nexmo)
    
    console.log(`Sending SMS to ${notification.recipient.phone}:`);
    console.log(`Message: ${notification.message}`);
    
    // Simulate SMS sending delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Get notification statistics
   */
  static async getNotificationStats(
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ stats: any }>> {
    const where: any = {};
    
    // Role-based filtering
    if (userRole !== 'ADMIN') {
      where.recipientId = userId;
    }

    const [
      total,
      unread,
      byType,
      byStatus,
      byPriority,
    ] = await Promise.all([
      prisma.notification.count({ where }),
      prisma.notification.count({ 
        where: { ...where, status: 'UNREAD' } 
      }),
      prisma.notification.groupBy({
        by: ['type'],
        where,
        _count: { type: true },
      }),
      prisma.notification.groupBy({
        by: ['status'],
        where,
        _count: { status: true },
      }),
      prisma.notification.groupBy({
        by: ['priority'],
        where,
        _count: { priority: true },
      }),
    ]);

    const stats = {
      total,
      unread,
      typeDistribution: byType.reduce((acc, item) => {
        acc[item.type] = item._count.type;
        return acc;
      }, {} as Record<string, number>),
      statusDistribution: byStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>),
      priorityDistribution: byPriority.reduce((acc, item) => {
        acc[item.priority] = item._count.priority;
        return acc;
      }, {} as Record<string, number>),
    };

    return {
      success: true,
      data: { stats },
    };
  }
}
