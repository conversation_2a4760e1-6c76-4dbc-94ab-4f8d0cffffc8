{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,2CAA8C;AAC9C,qCAAwE;AACxE,2CAAyE;AAGzE,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAM3B,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAgB,EAChB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,IAAA,4BAAsB,EAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAEhE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;QAGD,MAAM,OAAO,GAAG,IAAA,uBAAiB,EAAC,KAAK,CAAC,CAAC;QAGzC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;YAC7B,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,4BAAmB,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YACtD,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,CAAC,CAAC;QACjE,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAuC;YAClD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAxDW,QAAA,YAAY,gBAwDvB;AAMK,MAAM,SAAS,GAAG,CAAC,YAAiD,EAAE,EAAE;IAC7E,OAAO,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QACnE,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,2BAAkB,CAC1B,kCAAkC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC5D,CAAC;YACJ,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAlBW,QAAA,SAAS,aAkBpB;AAMK,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAgB,EAChB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,IAAA,4BAAsB,EAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAEhE,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAA,uBAAiB,EAAC,KAAK,CAAC,CAAC;gBAEzC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;oBAC7B,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,QAAQ,EAAE,IAAI;wBACd,KAAK,EAAE,IAAI;wBACX,IAAI,EAAE,IAAI;wBACV,SAAS,EAAE,IAAI;wBACf,QAAQ,EAAE,IAAI;wBACd,QAAQ,EAAE,IAAI;wBACd,WAAW,EAAE,IAAI;qBAClB;iBACF,CAAC,CAAC;gBAEH,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC;oBACnF,GAAG,CAAC,IAAI,GAAG;wBACT,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,IAAI,EAAE,IAAI,CAAC,IAAuC;wBAClD,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;qBACxB,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,MAAM,CAAC;YAET,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA7CW,QAAA,YAAY,gBA6CvB;AAMK,MAAM,sBAAsB,GAAG,KAAK,EACzC,GAAgB,EAChB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;QAGD,IAAI,GAAG,CAAC,IAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC/B,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;QAE9E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,2BAAkB,CAAC,qBAAqB,CAAC,CAAC;QACtD,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SAC5B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,2BAAkB,CAAC,mBAAmB,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,CAAC,IAAK,CAAC,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,2BAAkB,CAAC,sCAAsC,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAvCW,QAAA,sBAAsB,0BAuCjC"}