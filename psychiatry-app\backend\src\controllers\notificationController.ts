import { Response, NextFunction } from 'express';
import { z } from 'zod';
import { NotificationService } from '@/services/notificationService';
import { AuthRequest, CreateNotificationData } from '@/types';
import { createNotificationSchema } from '../utils/validation';

/**
 * Notification controller handling HTTP requests for notification operations
 */

// Validation schemas
const querySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  recipientId: z.string().uuid().optional(),
  type: z.string().optional(),
  status: z.string().optional(),
  priority: z.string().optional(),
  channel: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

const appointmentReminderSchema = z.object({
  appointmentId: z.string().uuid('Invalid appointment ID'),
});

const labResultNotificationSchema = z.object({
  labResultId: z.string().uuid('Invalid lab result ID'),
});

export class NotificationController {
  /**
   * Create a new notification
   * POST /api/notifications
   */
  static async createNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = createNotificationSchema.parse(req.body) as CreateNotificationData;

      const result = await NotificationService.createNotification(
        validatedData,
        req.user!.id
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all notifications for the current user
   * GET /api/notifications
   */
  static async getNotifications(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const query = querySchema.parse(req.query);
      const result = await NotificationService.getNotifications(query, req.user!.id, req.user!.role);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get notification by ID
   * GET /api/notifications/:id
   */
  static async getNotificationById(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const result = await NotificationService.getNotificationById(req.params.id, req.user!.id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mark a notification as read
   * PUT /api/notifications/:id/read
   */
  static async markAsRead(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const result = await NotificationService.markAsRead(req.params.id, req.user!.id, req.user!.role);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mark all notifications as read
   * PUT /api/notifications/read-all
   */
  static async markAllAsRead(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const result = await NotificationService.markAllAsRead(req.user!.id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a notification
   * DELETE /api/notifications/:id
   */
  static async deleteNotification(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }
      
      const result = await NotificationService.deleteNotification(req.params.id, req.user!.id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create appointment reminders
   * POST /api/notifications/appointment-reminders
   */
  static async createAppointmentReminders(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { appointmentId } = appointmentReminderSchema.parse(req.body);
      
      const result = await NotificationService.createAppointmentReminders(
        appointmentId,
        req.user!.id
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Send lab result notification
   * POST /api/notifications/lab-result
   */
  static async sendLabResultNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { labResultId } = labResultNotificationSchema.parse(req.body);
      
      const result = await NotificationService.sendLabResultNotification(
        labResultId,
        req.user!.id
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process scheduled notifications (internal endpoint for cron jobs)
   * POST /api/notifications/process-scheduled
   */
  static async processScheduledNotifications(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // This endpoint should be protected and only accessible by system/cron jobs
      // In production, you might want to use API keys or internal network restrictions
      
      const result = await NotificationService.processScheduledNotifications();

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get notification statistics
   * GET /api/notifications/stats
   */
  static async getNotificationStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const result = await NotificationService.getNotificationStats(
        req.user!.id,
        req.user!.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }
}
