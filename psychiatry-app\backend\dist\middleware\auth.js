"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizePatientAccess = exports.optionalAuth = exports.authorize = exports.authenticate = void 0;
const client_1 = require("@prisma/client");
const jwt_1 = require("@/utils/jwt");
const errors_1 = require("@/utils/errors");
const prisma = new client_1.PrismaClient();
const authenticate = async (req, res, next) => {
    try {
        const token = (0, jwt_1.extractTokenFromHeader)(req.headers.authorization);
        if (!token) {
            throw new errors_1.AuthenticationError('Access token required');
        }
        const payload = (0, jwt_1.verifyAccessToken)(token);
        const user = await prisma.user.findUnique({
            where: { id: payload.userId },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
                firstName: true,
                lastName: true,
                isActive: true,
                lockedUntil: true,
            },
        });
        if (!user) {
            throw new errors_1.AuthenticationError('User not found');
        }
        if (!user.isActive) {
            throw new errors_1.AuthenticationError('Account is deactivated');
        }
        if (user.lockedUntil && user.lockedUntil > new Date()) {
            throw new errors_1.AuthenticationError('Account is temporarily locked');
        }
        req.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            firstName: user.firstName,
            lastName: user.lastName,
        };
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.authenticate = authenticate;
const authorize = (allowedRoles) => {
    return (req, res, next) => {
        try {
            if (!req.user) {
                throw new errors_1.AuthenticationError('Authentication required');
            }
            if (!allowedRoles.includes(req.user.role)) {
                throw new errors_1.AuthorizationError(`Access denied. Required roles: ${allowedRoles.join(', ')}`);
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.authorize = authorize;
const optionalAuth = async (req, res, next) => {
    try {
        const token = (0, jwt_1.extractTokenFromHeader)(req.headers.authorization);
        if (token) {
            try {
                const payload = (0, jwt_1.verifyAccessToken)(token);
                const user = await prisma.user.findUnique({
                    where: { id: payload.userId },
                    select: {
                        id: true,
                        username: true,
                        email: true,
                        role: true,
                        firstName: true,
                        lastName: true,
                        isActive: true,
                        lockedUntil: true,
                    },
                });
                if (user && user.isActive && (!user.lockedUntil || user.lockedUntil <= new Date())) {
                    req.user = {
                        id: user.id,
                        username: user.username,
                        email: user.email,
                        role: user.role,
                        firstName: user.firstName,
                        lastName: user.lastName,
                    };
                }
            }
            catch {
            }
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.optionalAuth = optionalAuth;
const authorizePatientAccess = async (req, res, next) => {
    try {
        if (!req.user) {
            throw new errors_1.AuthenticationError('Authentication required');
        }
        if (req.user.role === 'ADMIN') {
            return next();
        }
        const patientId = req.params.id || req.params.patientId || req.body.patientId;
        if (!patientId) {
            throw new errors_1.AuthorizationError('Patient ID required');
        }
        const patient = await prisma.patient.findUnique({
            where: { id: patientId },
            select: { createdBy: true },
        });
        if (!patient) {
            throw new errors_1.AuthorizationError('Patient not found');
        }
        if (patient.createdBy !== req.user.id && !['ADMIN'].includes(req.user.role)) {
            throw new errors_1.AuthorizationError('Access denied to this patient record');
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.authorizePatientAccess = authorizePatientAccess;
//# sourceMappingURL=auth.js.map