# Repository Documentation

## Testing Framework
- **Primary Framework**: Playwright
- **Configuration**: `playwright.config.ts`
- **Test Directory**: `./tests`
- **Browser Support**: Chromium, Firefox, WebKit, Mobile Chrome, Mobile Safari

## Project Structure
- **Backend**: Node.js/Express with TypeScript, Prisma ORM
- **Frontend**: React/TypeScript with Vite
- **Database**: PostgreSQL with Prisma
- **Test Setup**: E2E testing with Playwright

## Development Setup
- Backend runs on port 3002
- Frontend runs on port 5173
- Tests configured to run against local development servers