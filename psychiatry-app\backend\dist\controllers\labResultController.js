"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LabResultController = void 0;
const zod_1 = require("zod");
const labResultService_1 = require("@/services/labResultService");
const validation_1 = require("../utils/validation");
const labResultQuerySchema = zod_1.z.object({
    page: zod_1.z.string().optional(),
    limit: zod_1.z.string().optional(),
    patientId: zod_1.z.string().uuid().optional(),
    testType: zod_1.z.string().optional(),
    status: zod_1.z.string().optional(),
    dateFrom: zod_1.z.string().optional(),
    dateTo: zod_1.z.string().optional(),
    sortBy: zod_1.z.string().optional(),
    sortOrder: zod_1.z.enum(['asc', 'desc']).optional(),
});
const patientLabResultsQuerySchema = zod_1.z.object({
    testType: zod_1.z.string().optional(),
    limit: zod_1.z.string().optional(),
    dateFrom: zod_1.z.string().optional(),
    dateTo: zod_1.z.string().optional(),
});
const trendsQuerySchema = zod_1.z.object({
    testType: zod_1.z.string().min(1, 'Test type is required'),
    dateFrom: zod_1.z.string().optional(),
    dateTo: zod_1.z.string().optional(),
});
class LabResultController {
    static async createLabResult(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const validatedData = validation_1.createLabResultSchema.parse(req.body);
            const result = await labResultService_1.LabResultService.createLabResult(validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getLabResults(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const query = labResultQuerySchema.parse(req.query);
            const result = await labResultService_1.LabResultService.getLabResults(query, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getLabResultById(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const result = await labResultService_1.LabResultService.getLabResultById(id, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getPatientLabResults(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { patientId } = req.params;
            const validatedQuery = patientLabResultsQuerySchema.parse(req.query);
            const queryOptions = {
                testType: validatedQuery.testType,
                limit: validatedQuery.limit ? parseInt(validatedQuery.limit, 10) : undefined,
                dateFrom: validatedQuery.dateFrom,
                dateTo: validatedQuery.dateTo,
            };
            const result = await labResultService_1.LabResultService.getPatientLabResults(patientId, req.user.id, req.user.role, queryOptions);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getLabResultTrends(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { patientId } = req.params;
            const validatedQuery = trendsQuerySchema.parse(req.query);
            const result = await labResultService_1.LabResultService.getLabResultTrends(patientId, validatedQuery.testType, req.user.id, req.user.role, validatedQuery.dateFrom, validatedQuery.dateTo);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async updateLabResult(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const validatedData = validation_1.updateLabResultSchema.parse(req.body);
            const result = await labResultService_1.LabResultService.updateLabResult(req.params.id, validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async deleteLabResult(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const auditData = {
                ipAddress: req.ip,
                userAgent: req.get('User-Agent'),
            };
            const result = await labResultService_1.LabResultService.deleteLabResult(id, req.user.id, req.user.role, auditData);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.LabResultController = LabResultController;
//# sourceMappingURL=labResultController.js.map